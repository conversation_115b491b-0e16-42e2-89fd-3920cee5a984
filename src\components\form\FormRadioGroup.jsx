import { RadioGroup } from "@heroui/react";
import PropTypes from "prop-types";
import { useController } from "react-hook-form";
/**
 * @param {FormRadioGroupProps} props
 */
const FormRadioGroup = (props) => {
  const { name, control, renderItem, items, children, radioGroupProps } = props;

  const {
    field: { onChange, ...field },
    fieldState,
  } = useController({ name, control });

  return (
    <RadioGroup
      {...field}
      isInvalid={!!fieldState.error}
      onValueChange={onChange}
      description={fieldState.error?.message}
      {...(({ children, ...rest }) => rest)(radioGroupProps || {})}
    >
      {items
        ? items.map((item) => renderItem?.(item))
        : children || radioGroupProps?.children}
    </RadioGroup>
  );
};

FormRadioGroup.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  items: PropTypes.array,
  renderItem: PropTypes.func,
  children: PropTypes.node,
  radioGroupProps: PropTypes.object,
};

export default FormRadioGroup;
