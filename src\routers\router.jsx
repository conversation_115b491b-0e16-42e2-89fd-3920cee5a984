import { QueryClient } from "@tanstack/react-query";
import Cookies from "js-cookie";
import { createBrowserRouter, replace } from "react-router";
import App from "../App";
import api from "../api";
import LoginPage from "../views/auth/LoginPage";
import Error404Page from "../views/errors/Error404Page";
import UnauthorizedPage from "../views/errors/UnauthorizedPage";
import AboutPage from "../views/home/<USER>";
import DashboardPage from "../views/home/<USER>";
import DashboardLayout from "../views/layouts/DashboardLayout";
import CreateOrganizationsPage from "../views/organizations/CreateOrganizationsPage";
import OrganizationsPage from "../views/organizations/OrganizationsPage";
import ReportStudentPage from "../views/students/ReportStudentPage";
import StudentsPage from "../views/students/StudentsPage";
const TOKEN_KEY = import.meta.env.VITE_TOKEN_KEY;
export const queryClient = new QueryClient();
export const router = createBrowserRouter([
  {
    path: "",
    loader: async ({ request }) => {
      const url = new URL(request.url);
      const isLoginPage = url.pathname === "/login";
      const hasToken = Cookies.get(TOKEN_KEY);

      if (!hasToken && !isLoginPage) {
        return replace("/login");
      }

      const userPromise = queryClient
        .fetchQuery({
          queryKey: api.Auth.me.getKey(),
          queryFn: api.Auth.me.fetcher,
          retry: 0,
        })
        .catch((e) => {
          if (hasToken) {
            Cookies.remove(TOKEN_KEY);
          }
          return { error: e, status: "error" };
        })
        .then((data) => {
          if (data && !data.error) {
            return { data, status: "success" };
          }
          return { data: null, status: "error" };
        });

      const result = await userPromise;
      if (result.status === "success" && isLoginPage) {
        return replace("/");
      }

      if (result.status === "error" && !isLoginPage) {
        return replace("/login");
      }

      return { user: userPromise };
    },
    element: <App />,
    children: [
      {
        path: "/login",
        handle: { title: "ورود و ثبت نام" },
        element: <LoginPage />,
      },
      {
        path: "/",
        handle: { haveBreadcrumb: false },
        element: <DashboardLayout />,
        children: [
          {
            index: true,
            handle: { title: "داشبورد", haveBreadcrumb: false },
            element: <DashboardPage />,
          },
          {
            path: "/about",
            handle: { title: "درباره سازمان" },
            element: <AboutPage />,
          },
          {
            path: "/students",
            handle: { title: "زبان آموزها" },
            children: [
              {
                index: true,
                handle: { title: "زبان آموزها", haveBreadcrumb: false },
                element: <StudentsPage />,
              },
              {
                path: "/students/:id/reports",
                handle: { title: "جزئیات زبان آموز" },
                element: <ReportStudentPage />,
              },
            ],
          },
          {
            path: "/organizations",
            handle: { title: "سازمان ها" },
            children: [
              {
                index: true,
                handle: { title: "سازمان ها", haveBreadcrumb: false },
                element: <OrganizationsPage />,
              },
              {
                path: "/organizations/create",
                handle: { title: "ایجاد سازمان جدید" },
                element: <CreateOrganizationsPage />,
              },
            ],
          },
        ],
      },
    ],
  },

  {
    path: "/unauthorized",
    handle: { title: "عدم دسترسی" },
    element: <UnauthorizedPage />,
  },
  {
    path: "*",
    handle: { title: "!صفحه پیدا نشد" },
    element: <Error404Page />,
  },
]);
