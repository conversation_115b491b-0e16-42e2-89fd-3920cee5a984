import { Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";

const renderLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  color,
}) => {
  if (percent * 100 < 5) return null;

  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) / 2;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill={`hsl(var(--heroui-${color}-foreground))`}
      className="pointer-events-none select-none text-sm font-medium"
      textAnchor="middle"
      dominantBaseline="central"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};
const PieChartCard = ({
  data,
  pies,
  className,
  size = 200,
  title,
  isLoading,
}) => {
  return (
    <div
      className={cn(
        "flex flex-col transition-all justify-between rounded-small bg-background pt-5 pb-4 px-4 items-center ",
        className,
      )}
    >
      {!isLoading ? (
        <p className="mb-2 font-medium">{title}</p>
      ) : (
        <Skeleton className="h-4 max-w-xs w-full rounded-lg" />
      )}

      {!isLoading ? (
        <ResponsiveContainer
          minWidth={size}
          minHeight={size}
          height={"65%"}
          width={"65%"}
        >
          <PieChart margin={{ bottom: 14 }}>
            <Pie
              dataKey="value"
              data={data}
              blendStroke
              className="focus:outline-none"
              startAngle={90}
              label={renderLabel}
              animationDuration={600}
              animationBegin={0}
              animationEasing="ease-out"
              endAngle={-270}
              labelLine={false}
              cx="50%"
              cy="50%"
              innerRadius="40%"
              outerRadius="90%"
            >
              {data.map((entry, index) => {
                return (
                  <Cell
                    key={index}
                    fill={`hsl(var(--heroui-${pies[index]}))`}
                    color={pies[index]}
                    className={cn(
                      "origin-center outline-none transition-transform duration-200 ease-out hover:scale-105 hover:drop-shadow-md focus:outline-none",
                    )}
                  />
                );
              })}
            </Pie>
            <Tooltip
              allowEscapeViewBox={{ x: true, y: false }}
              content={({ payload }) => renderTooltipContent(payload)}
            />
          </PieChart>
        </ResponsiveContainer>
      ) : (
        <Skeleton className={"w-3/5 my-4 aspect-square rounded-full"} />
      )}

      {!isLoading ? (
        <div className="flex items-center gap-6 ">
          {data.map((entry, index) => {
            return (
              <div key={index} className="flex items-center gap-2">
                <span
                  className="mt-1 size-3 rounded-full"
                  style={{
                    backgroundColor: `hsl(var(--heroui-${pies[index]}))`,
                  }}
                />
                <p className="text-sm font-medium">{entry.title}</p>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex items-center w-full gap-4">
          <Skeleton className="h-4 max-w-xs w-full rounded-lg" />
          <Skeleton className="h-4 max-w-xs w-full rounded-lg" />
        </div>
      )}
    </div>
  );
};

PieChartCard.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      value: PropTypes.number,
    }),
  ),
  pies: PropTypes.arrayOf(PropTypes.string),
  className: PropTypes.string,
  size: PropTypes.number,
  title: PropTypes.string,
  description: PropTypes.string,
  isLoading: PropTypes.bool,
};

export default PieChartCard;

const renderTooltipContent = (payload) => {
  if (!payload || payload.length === 0) return null;
  const data = payload[0].payload;
  return (
    <div className="rtl flex min-w-20 flex-col items-center justify-start rounded-md bg-background shadow-small">
      <div className="w-full rounded-t-md bg-background-100 p-1 text-center">
        <p>{data.title}</p>
      </div>

      <div className="flex items-center justify-center gap-1 p-2 text-center">
        <p>{new Intl.NumberFormat("fa-IR").format(data["value"])}</p>
        {/* <TomanIcon className="size-3" /> */}
      </div>
    </div>
  );
};
