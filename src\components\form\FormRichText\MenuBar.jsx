import { cn } from "@heroui/react";
import PropTypes from "prop-types";
import { forwardRef } from "react";
import {
  AlignmentButton,
  BoldButton,
  HeadingButton,
  ImageButton,
  ItalicButton,
  LinkButton,
  OrderedListButton,
  RedoButton,
  StrikeButton,
  UnOrderedListButton,
  UnderlineButton,
  UndoButton,
} from "./EditorControllers";

const MenuBar = forwardRef(({ editor, className }, ref) => {
  return (
    <ul
      ref={ref}
      className={cn(
        "flex w-full flex-wrap items-center divide-x-2 rtl:divide-x-reverse",
        className,
      )}
    >
      {/* Text Formatting Controls */}
      <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
        <BoldButton editor={editor} />
        <ItalicButton editor={editor} />
        <UnderlineButton editor={editor} />
        <StrikeButton editor={editor} />
      </li>

      {/* heading dropdown */}
      <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
        <HeadingButton editor={editor} />
      </li>

      {/* alignment dropdown  */}
      <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
        <AlignmentButton editor={editor} />
      </li>

      {/* list buttons  */}
      <li className="space-x-2 text-danger space-x-reverse px-2 first:ps-0 last:pe-0">
        <UnOrderedListButton editor={editor} />
        <OrderedListButton editor={editor} />
      </li>

      {/* image and link buttons  */}
      <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
        <ImageButton editor={editor} />
        <LinkButton editor={editor} />
      </li>
      {/* undo and redo button  */}
      <li className="space-x-2 space-x-reverse px-2 first:ps-0 last:pe-0">
        <UndoButton editor={editor} />
        <RedoButton editor={editor} />
      </li>
    </ul>
  );
});

MenuBar.displayName = "MenuBar";

export default MenuBar;

MenuBar.propTypes = {
  editor: PropTypes.object.isRequired,
  className: PropTypes.string,
};
