import { Autocomplete, cn } from "@heroui/react";
import { useDebounce } from "@uidotdev/usehooks";
import { SearchNormal1 } from "iconsax-reactjs";
import { useQueryState } from "nuqs";

import PropTypes from "prop-types";
import { useEffect, useState } from "react";
import { useController } from "react-hook-form";

/**
 * @param {FormAutoCompleteProps} props
 */
export default function FormAutoComplete(props) {
  const {
    control,
    name,
    items,
    autoCompleteProps,
    renderItem,
    isFilterable = false,
  } = props;

  const [, setFilter] = useQueryState(name, { defaultValue: "" });
  const [inputValue, setInputValue] = useState("");
  const debouncedValue = useDebounce(inputValue, 2000);

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
  });

  useEffect(() => {
    if (isFilterable) {
      setFilter(debouncedValue || null);
    }
  }, [debouncedValue, isFilterable]);

  const handleValueChange = (val) => {
    if (autoCompleteProps?.multiple) {
      const newValue = Array.isArray(value) ? value : [];
      if (val !== null) {
        const index = newValue.findIndex((item) => item === val);
        if (index !== -1) {
          newValue.splice(index, 1);
          onChange([...newValue]);
        } else {
          onChange([...newValue, val]);
        }
      }
    } else {
      onChange(val);
    }
  };

  return (
    <Autocomplete
      {...field}
      value={value}
      defaultItems={items}
      startContent={
        <SearchNormal1 className={"size-5 shrink-0 text-foreground-300"} />
      }
      onSelectionChange={handleValueChange}
      onInputChange={setInputValue}
      inputValue={inputValue}
      inputProps={{
        classNames: {
          inputWrapper: cn(
            "hover:bg-background-50 dark:hover:bg-background-50",
            { "bg-background-100": !fieldState.error },
            autoCompleteProps?.inputProps?.classNames?.inputWrapper,
          ),
          ...(({ inputWrapper, ...rest }) => rest)(
            autoCompleteProps?.inputProps?.classNames || {},
          ),
        },
      }}
      {...autoCompleteProps}
    >
      {renderItem && renderItem}
    </Autocomplete>
  );
}

FormAutoComplete.propTypes = {
  control: PropTypes.object.isRequired,
  autoCompleteProps: PropTypes.object,
  name: PropTypes.string.isRequired,
  items: PropTypes.array.isRequired,
  renderItem: PropTypes.func,
  isFilterable: PropTypes.bool,
};
