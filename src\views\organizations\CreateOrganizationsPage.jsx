import { AutocompleteItem, Avatar, Button, Radio } from "@heroui/react";
import { Add, Edit, Trash } from "iconsax-reactjs";
import { Form, useForm } from "react-hook-form";
import FormAutoComplete from "../../components/form/FormAutoComplete";
import FormDatePicker from "../../components/form/FormDatePicker";
import FormInput from "../../components/form/FormInput";
import FormRadioGroup from "../../components/form/FormRadioGroup";
import FormRichText from "../../components/form/FormRichText/FormRichText";
import FormSwitch from "../../components/form/FormSwitch";
import FormUpload from "../../components/form/FormUpload";
import FormUploadAvatar from "../../components/form/FormUploadAvatar";
import Icon from "../../components/icon/Icon";
import PageWrapper from "../../components/layout/PageWrapper";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CEO",
    phone: "Management",
    status: "active",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/1.png",
    email: "<EMAIL>",
  },
  {
    id: 2,
    name: "Zoey Lang",
    role: "Tech Lead",
    phone: "Development",
    status: "paused",
    age: "25",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/1.png",
    email: "<EMAIL>",
  },
  {
    id: 3,
    name: "Jane Fisher",
    role: "Sr. Dev",
    phone: "Development",
    status: "active",
    age: "22",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/2.png",
    email: "<EMAIL>",
  },
  {
    id: 4,
    name: "William Howard",
    role: "C.M.",
    phone: "Marketing",
    status: "vacation",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/2.png",
    email: "<EMAIL>",
  },
  {
    id: 5,
    name: "Kristen Copper",
    role: "S. Manager",
    phone: "Sales",
    status: "active",
    age: "24",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/3.png",
    email: "<EMAIL>",
  },
  {
    id: 6,
    name: "Brian Kim",
    role: "P. Manager",
    phone: "Management",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/3.png",
    email: "<EMAIL>",
    status: "active",
  },
  {
    id: 7,
    name: "Michael Hunt",
    role: "Designer",
    phone: "Design",
    status: "paused",
    age: "27",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/4.png",
    email: "<EMAIL>",
  },
  {
    id: 8,
    name: "Samantha Brooks",
    role: "HR Manager",
    phone: "HR",
    status: "active",
    age: "31",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/4.png",
    email: "<EMAIL>",
  },
  {
    id: 9,
    name: "Frank Harrison",
    role: "F. Manager",
    phone: "Finance",
    status: "vacation",
    age: "33",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/5.png",
    email: "<EMAIL>",
  },
  {
    id: 10,
    name: "Emma Adams",
    role: "Ops Manager",
    phone: "Operations",
    status: "active",
    age: "35",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/5.png",
    email: "<EMAIL>",
  },
  {
    id: 11,
    name: "Brandon Stevens",
    role: "Jr. Dev",
    phone: "Development",
    status: "active",
    age: "22",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/7.png",
    email: "<EMAIL>",
  },
  {
    id: 12,
    name: "Megan Richards",
    role: "P. Manager",
    phone: "Product",
    status: "paused",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/7.png",
    email: "<EMAIL>",
  },
  {
    id: 13,
    name: "Oliver Scott",
    role: "S. Manager",
    phone: "Security",
    status: "active",
    age: "37",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/8.png",
    email: "<EMAIL>",
  },
  {
    id: 14,
    name: "Grace Allen",
    role: "M. Specialist",
    phone: "Marketing",
    status: "active",
    age: "30",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/8.png",
    email: "<EMAIL>",
  },
  {
    id: 15,
    name: "Noah Carter",
    role: "IT Specialist",
    phone: "I. Technology",
    status: "paused",
    age: "31",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/9.png",
    email: "<EMAIL>",
  },
  {
    id: 16,
    name: "Ava Perez",
    role: "Manager",
    phone: "Sales",
    status: "active",
    age: "29",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/9.png",
    email: "<EMAIL>",
  },
  {
    id: 17,
    name: "Liam Johnson",
    role: "Data Analyst",
    phone: "Analysis",
    status: "active",
    age: "28",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/11.png",
    email: "<EMAIL>",
  },
  {
    id: 18,
    name: "Sophia Taylor",
    role: "QA Analyst",
    phone: "Testing",
    status: "active",
    age: "27",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/11.png",
    email: "<EMAIL>",
  },
  {
    id: 19,
    name: "Lucas Harris",
    role: "Administrator",
    phone: "Information Technology",
    status: "paused",
    age: "32",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/male/12.png",
    email: "<EMAIL>",
  },
  {
    id: 20,
    name: "Mia Robinson",
    role: "Coordinator",
    phone: "Operations",
    status: "active",
    age: "26",
    avatar: "https://d2u8k2ocievbld.cloudfront.net/memojis/female/12.png",
    email: "<EMAIL>",
  },
];

const CreateOrganizationsPage = () => {
  const { control, watch, setValue, handleSubmit } = useForm({
    defaultValues: {
      users: [],
    },
  });

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-col gap-6  rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <FormUploadAvatar
            name="image"
            control={control}
            classNames={{
              wrapper: "max-h-28 self-center md:self-auto max-w-28",
            }}
          />

          <p className="font-medium mt-2">اطلاعات سازمان</p>

          <div className="flex items-center flex-wrap md:flex-nowrap gap-4">
            <FormInput
              control={control}
              name="organizationName"
              type="text"
              inputProps={{
                classNames: {
                  base: "max-w-sm",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
                size: "lg",
                radius: "full",
                placeholder: "نام سازمان",
                startContent: <Edit className="size-6 text-primary" />,
              }}
            />
            <FormDatePicker
              control={control}
              name="date"
              isRange={true}
              size="lg"
              radius="full"
              startContent={
                <Icon className={"text-primary size-7"} name={"accept-note"} />
              }
              classNames={{
                base: "max-w-sm",
                inputWrapper:
                  "shadow-sm hover:!bg-background-100 border hover:border-foreground-200 transition-colors border-foreground-100",
                input: "text-sm ",
              }}
            />
          </div>

          <p className="font-medium mt-2">ویدیوی مرتبط با سازمان</p>

          <FormUpload control={control} name="video" />

          <p className="font-medium mt-2">توضیحات مرتبط با سازمان</p>
          <FormRichText
            control={control}
            name="description"
            enabledButtons={{
              image: false,
              link: false,
            }}
          />

          <FormSwitch
            control={control}
            className="ltr"
            name="active"
            label="وضعیت سازمان"
          />
        </div>

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <div className="flex w-full md:flex-nowrap flex-wrap items-center gap-4">
            <FormAutoComplete
              control={control}
              name="users"
              items={users}
              isFilterable={true}
              renderItem={(item) => (
                <AutocompleteItem
                  classNames={{
                    selectedIcon: "hidden",
                    base: "hover:!bg-foreground-100/80 data-[hover=true]:bg-foreground-100/80",
                  }}
                  key={item.id}
                  textValue={item.name}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex gap-2 items-center">
                      <Avatar
                        alt={item.name}
                        className="flex-shrink-0"
                        src={item.avatar}
                      />
                      <div className="flex flex-col">
                        <span className="text-small ">{item.name}</span>
                        <span className="text-tiny text-default-400">
                          {item.phone}
                        </span>
                      </div>
                    </div>
                    <Button radius="full" size="sm" color="primary">
                      افزودن
                    </Button>
                  </div>
                </AutocompleteItem>
              )}
              autoCompleteProps={{
                fullWidth: true,
                isClearable: false,
                multiple: true,
                listboxProps: {
                  emptyContent: "چیزی پیدا نشد",
                },
                // isLoading: true,
                inputProps: {
                  classNames: {
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                    input: "text-sm",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "جستجوی ادمین با استفاده از شماره موبایل",
                },
              }}
            />
            <Button
              type="button"
              color="primary"
              radius="full"
              className="px-6 shrink-0"
              startContent={<Add className="size-6" />}
            >
              ایجاد ادمین جدید
            </Button>
          </div>

          <ul className="space-y-4">
            {watch("users")?.map((userId) => {
              const user = users.find((u) => u.id.toString() === userId);
              return user ? (
                <li
                  key={user.id}
                  className="flex items-center rounded-3xl gap-4 p-6 border border-foreground-100 shadow-sm"
                >
                  <Avatar
                    alt={user.name}
                    className="flex-shrink-0"
                    src={user.avatar}
                    size="lg"
                  />

                  <div className="flex items-center font-medium gap-2">
                    <p className="text-foreground-400">نام و نام خانوادگی: </p>
                    <p>{user.name}</p>
                  </div>

                  <div className="flex items-center font-medium gap-2">
                    <p className="text-foreground-400">شماره تماس: </p>
                    <p>{user.phone}</p>
                  </div>

                  <Button
                    className="ms-auto"
                    isIconOnly
                    radius="full"
                    color="danger"
                    variant="light"
                    onPress={() => {
                      setValue(
                        "users",
                        watch("users").filter((u) => u !== userId),
                      );
                    }}
                  >
                    <Trash />
                  </Button>
                </li>
              ) : null;
            })}
          </ul>

          <p className="font-medium mt-2">نقش</p>

          <div className="flex items-end md:flex-nowrap flex-wrap gap-3">
            <FormInput
              control={control}
              name="rolePersianName"
              inputProps={{
                placeholder: "عنوان نقش",
                // labelPlacement: "outside-left",
                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
            <FormInput
              control={control}
              name="roleLatinName"
              inputProps={{
                placeholder: "نام لاتین",
                // labelPlacement: "outside-left",
                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
          </div>

          <p className="font-medium mt-2">کاربران</p>

          <FormRadioGroup
            control={control}
            name="roleUsers"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <p className="font-medium mt-2">پشتیبانی</p>

          <FormRadioGroup
            control={control}
            name="roleSupport"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <p className="font-medium mt-2">سازمان</p>

          <FormRadioGroup
            control={control}
            name="roleOrganization"
            radioGroupProps={{
              orientation: "horizontal",
              classNames: {
                wrapper: "space-x-3 space-x-reverse",
              },
            }}
            items={[
              {
                key: "all",
                value: "all",
                label: "انتخاب همه",
              },
              {
                key: "viewers",
                value: "viewers",
                label: "مشاهده کاربران",
              },
              {
                key: "single_user",
                value: "single_user",
                label: "مشاهده عملکرد یک کاربر",
              },
            ]}
            renderItem={(item) => {
              return (
                <Radio
                  value={item.value}
                  key={item.key}
                  size="lg"
                  color="primary"
                  classNames={{
                    label: "text-sm font-medium text-inherit",
                    base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                  }}
                >
                  {item.label || item.value}
                </Radio>
              );
            }}
          />
          <div className="flex gap-3 justify-end items-center">
            <Button
              type="submit"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              انصراف
            </Button>
            <Button
              type="submit"
              color="primary"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              ثبت سازمان
            </Button>
          </div>
        </div>
      </Form>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
