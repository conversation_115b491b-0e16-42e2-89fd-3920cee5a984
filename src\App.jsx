import { Suspense, useEffect } from "react";
import { Await, Outlet, useLoaderData } from "react-router";
import useUserStore from "./stores/useUserStore";
import LoadingPage from "./views/errors/LoadingPage";
import UnauthorizedPage from "./views/errors/UnauthorizedPage";

function App() {
  const { user } = useLoaderData();
  const { setUser } = useUserStore();

  useEffect(() => {
    if (user.data && user.status === "success") {
      setUser(user.data?.data);
    }
  }, [user]);

  return (
    <Suspense fallback={<LoadingPage />}>
      <Await resolve={user} errorElement={<UnauthorizedPage />}>
        {() => {
          return <Outlet />;
        }}
      </Await>
    </Suspense>
  );
}

export default App;
