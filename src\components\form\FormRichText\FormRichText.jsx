import { Input, cn } from "@heroui/react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EditorContent, useEditor } from "@tiptap/react";
import { useIntersectionObserver } from "@uidotdev/usehooks";
import { Link } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useCallback, useState } from "react";
import { useController } from "react-hook-form";
import BubbleBar from "./BubbleBar";
import extensions from "./Extensions";
import MenuBar from "./MenuBar";

const FormRichText = (props) => {
  const { control, name, className, enabledButtons = {} } = props;

  // Default enabled buttons - all buttons are enabled by default
  const defaultButtons = {
    bold: true,
    italic: true,
    underline: true,
    strike: true,
    heading: true,
    alignment: true,
    unorderedList: true,
    orderedList: true,
    image: true,
    link: true,
    undo: true,
    redo: true,
  };

  // Merge with provided enabledButtons
  const buttons = { ...defaultButtons, ...enabledButtons };

  const [ref, entry] = useIntersectionObserver({
    threshold: 0,
    root: null,
    rootMargin: "0px",
  });

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
  });

  const editor = useEditor({
    extensions,
    content: value,
    editorProps: {
      attributes: {
        class:
          "prose prose lg:prose-lg prose-headings:mt-0 prose-p:mt-0 prose-a:mt-0 prose-ol:my-0 prose-ul:my-0 min-h-48 max-w-full focus:outline-none rounded-medium border bg-background p-4",
      },
    },
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML());
    },
  });

  const [href, setHref] = useState("");

  const shouldShow = useCallback(() => {
    const isActive = editor?.isActive("link");
    setHref(editor?.getAttributes("link")?.href || "");

    return !!isActive && editor.state.selection.empty;
  }, [editor]);

  return (
    <div
      className={cn(
        "flex flex-col gap-2 rounded-medium bg-background-100 p-2",
        className,
      )}
    >
      <MenuBar ref={ref} editor={editor} enabledButtons={enabledButtons} />

      <BubbleBar
        isVisible={!entry?.isIntersecting}
        editor={editor}
        enabledButtons={enabledButtons}
      />

      {/* Link Bubble Menu - Only show if link button is enabled */}
      {buttons.link && (
        <BubbleMenu
          editor={editor}
          shouldShow={shouldShow}
          className="flex items-center gap-2 rounded-md bg-background p-1.5 shadow-md"
        >
          <Input
            value={href}
            onChange={(e) => {
              setHref(e.target.value);
            }}
            onBlur={() => {
              if (href.trim()) {
                editor
                  .chain()
                  .focus()
                  .extendMarkRange("link")
                  .setLink({ href: href })
                  .run();
              } else {
                editor.chain().focus().unsetLink().run();
              }
            }}
            placeholder="ویرایش لینک"
            size="sm"
            variant="bordered"
            radius="sm"
            classNames={{
              label: "text-foreground-600 font-medium",
              input: "text-foreground-700",
            }}
            endContent={
              <Link className="pointer-events-none size-4 flex-shrink-0 text-foreground-400" />
            }
          />
        </BubbleMenu>
      )}

      <EditorContent editor={editor} />
    </div>
  );
};

FormRichText.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  className: PropTypes.string,
  enabledButtons: PropTypes.object,
};

export default FormRichText;
