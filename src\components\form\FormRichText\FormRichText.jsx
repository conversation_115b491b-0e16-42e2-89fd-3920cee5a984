import PropTypes from 'prop-types';
import extensions from './Extensions';
import { useIntersectionObserver } from '@uidotdev/usehooks';
import { useController } from 'react-hook-form';
import { B<PERSON>bleMenu, EditorContent, useEditor } from '@tiptap/react';
import { useCallback, useState } from 'react';
import { cn, Input } from '@heroui/react';
import MenuBar from './MenuBar';
import BubbleBar from './BubbleBar';
import { Link } from 'iconsax-reactjs';


const FormRichText = (props) => {
  const { control, name, className } = props;

  const [ref, entry] = useIntersectionObserver({
    threshold: 0,
    root: null,
    rootMargin: '0px',
  });

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({
    name,
    control,
  });

  const editor = useEditor({
    extensions,
    content: value,
    editorProps: {
      attributes: {
        class:
          'prose prose lg:prose-lg prose-headings:mt-0 prose-p:mt-0 prose-a:mt-0 prose-ol:my-0 prose-ul:my-0 min-h-48 max-w-full focus:outline-none rounded-medium border bg-background p-4',
      },
    },
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML());
    },
  });

  const [href, setHref] = useState('');

  const shouldShow = useCallback(() => {
    const isActive = editor?.isActive('link');
    setHref(editor?.getAttributes('link')?.href || '');

    return !!isActive && editor.state.selection.empty;
  }, [editor]);

  return (
    <div
      className={cn(
        'flex flex-col gap-2 rounded-medium bg-background-100 p-2',
        className,
      )}>
      <MenuBar ref={ref} editor={editor} />

      <BubbleBar isVisible={!entry?.isIntersecting} editor={editor} />

      {/* Link Bubble Menu */}
      <BubbleMenu
        editor={editor}
        shouldShow={shouldShow}
        className='flex items-center gap-2 rounded-md bg-background p-1.5 shadow-md'>
        <Input
          value={href}
          onChange={(e) => {
            setHref(e.target.value);
          }}
          onBlur={() => {
            if (href.trim()) {
              editor
                .chain()
                .focus()
                .extendMarkRange('link')
                .setLink({ href: href })
                .run();
            } else {
              editor.chain().focus().unsetLink().run();
            }
          }}
          placeholder='ویرایش لینک'
          size='sm'
          variant='bordered'
          radius='sm'
          classNames={{
            label: 'text-foreground-600 font-medium',
            input: 'text-foreground-700',
          }}
          endContent={
            <Link className='pointer-events-none size-4 flex-shrink-0 text-foreground-400' />
          }
        />
      </BubbleMenu>

      <EditorContent editor={editor} />
    </div>
  );
};

FormRichText.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  className: PropTypes.string,
};

export default FormRichText;
