import { BubbleMenu } from "@tiptap/react";
import MenuBar from "./MenuBar";
import PropTypes from "prop-types";


const BubbleBar = ({ editor, isVisible }) => {
  return (
    <BubbleMenu
      tippyOptions={{ zIndex: 10 }}
      className={isVisible ? '' : 'hidden'}
      editor={editor}>
      <MenuBar
        editor={editor}
        className='w-max max-w-full rounded-medium bg-background-100 p-2'
      />
    </BubbleMenu>
  );
};

BubbleBar.propTypes = {
  editor: PropTypes.object.isRequired,
  isVisible: PropTypes.bool.isRequired,
};

export default BubbleBar;
