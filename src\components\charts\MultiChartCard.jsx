import PersianDate from "@alireza-ab/persian-date";
import { Skeleton, cn } from "@heroui/react";
import { useMediaQuery } from "@uidotdev/usehooks";

import PropTypes from "prop-types";
import { Fragment } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

/**
 * A card component that displays multiple line/bar charts.
 *
 * @param {array} charts An array of objects containing the data key and color for each chart.
 * @param {element} topContent The content to be displayed at the top of the card.
 * @param {string} title The title of the card.
 * @param {string} description The description of the card.
 * @param {array} data The data for the charts.
 * @param {string} className The class name for the card.
 * @param {string} type The type of charts to display. Can be 'bar' or 'area'. Default is 'area'.
 * @param {function} xAxisTickFormatter A function to format the X-axis ticks. Receives the tick value and index.
 */

const MultiChartCard = ({
  charts,
  title,
  data,
  className,
  type = "area",
  xAxis<PERSON>ick<PERSON>ormatter,
  CustomXTick,
  isLoading,
}) => {
  const Component = type.toUpperCase() === "BAR" ? BarChart : AreaChart;
  const Comp = type.toUpperCase() === "BAR" ? Bar : Area;

  const isMobile = useMediaQuery("(max-width:600px)");
  const isTablet = useMediaQuery("(max-width:900px)");

  const monthNames = [
    "فروردین",
    "اردیبهشت",
    "خرداد",
    "تیر",
    "مرداد",
    "شهریور",
    "مهر",
    "آبان",
    "آذر",
    "دی",
    "بهمن",
    "اسفند",
  ];

  return (
    <div
      className={cn(
        "flex flex-col transition-all justify-between rounded-small bg-background pt-5 pb-4 px-4 items-center ",
        className,
      )}
    >
      <div className="flex items-center justify-between w-full">
        {!isLoading ? (
          <p className="mb-2 font-medium">{title}</p>
        ) : (
          <Skeleton className="h-4 max-w-xs w-full rounded-lg" />
        )}
        {!isLoading ? (
          <div className="flex items-center gap-6 pe-4 ">
            {charts.map((entry) => {
              return (
                <div key={entry.title} className="flex items-center gap-2">
                  <span
                    className="mb-1 size-3 rounded-full"
                    style={{
                      backgroundColor: `hsl(var(--heroui-${entry.color}))`,
                    }}
                  />
                  <p className="text-sm font-medium">{entry.title}</p>
                </div>
              );
            })}
          </div>
        ) : (
          <Skeleton className="h-4 w-28 rounded-lg" />
        )}
      </div>

      {!isLoading ? (
        <ResponsiveContainer
          className={"ltr mt-2"}
          minHeight={300}
          width={"100%"}
          height={"100%"}
        >
          <Component data={data}>
            <defs>
              {charts.map((chart) => {
                return (
                  <linearGradient
                    key={chart.dataKey}
                    id={chart.dataKey}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor={`hsl(var(--heroui-${chart.color}))`}
                      stopOpacity={0.1}
                    />
                    <stop
                      offset="85%"
                      stopColor={`hsl(var(--heroui-${chart.color}))`}
                      stopOpacity={0}
                    />
                  </linearGradient>
                );
              })}
            </defs>
            <XAxis
              tickLine={false}
              axisLine={false}
              reversed={false}
              dataKey="date"
              fontSize={"12"}
              interval={0}
              direction={"rtl"}
              {...(isMobile && {
                angle: -45,
                textAnchor: "start",
                height: 70,
              })}
              {...(isTablet && {
                angle: -45,
                textAnchor: "start",
                height: 70,
              })}
              {...(CustomXTick && {
                tick: (props) => (
                  <CustomXTick
                    {...props}
                    isMobile={isMobile}
                    isTablet={isTablet}
                  />
                ),
              })}
              tickFormatter={
                xAxisTickFormatter
                  ? xAxisTickFormatter
                  : (tick) => {
                      const date = new Date(tick);
                      const persianDate = new PersianDate(date);
                      const month = persianDate.month();
                      const day = persianDate.date();
                      return `${day} ${monthNames[month - 1]}`;
                    }
              }
            />

            <YAxis
              tickLine={false}
              axisLine={false}
              tickSize={10}
              tickCount={5}
              orientation="left"
              fontSize={"14"}
              tickMargin={10}
              width={40}
            />
            <CartesianGrid vertical={false} strokeDasharray="2 3" />
            <Tooltip
              cursor={
                type === "bar" ? { fill: "gray", fillOpacity: 0.15 } : "auto"
              }
              content={({ payload }) => renderTooltipContent(payload, charts)}
            />

            {charts.map((chart) => {
              return (
                <Comp
                  key={chart.dataKey}
                  type="monotone"
                  dataKey={chart.dataKey}
                  stroke={`hsl(var(--heroui-${chart.color}))`}
                  fillOpacity={1}
                  strokeWidth={2}
                  maxBarSize={20}
                  fill={
                    type === "bar"
                      ? `hsl(var(--heroui-${chart.color}))`
                      : `url(#${chart.dataKey})`
                  }
                  activeDot={{
                    stroke: `hsl(var(--heroui-${chart.color}))`,
                    strokeWidth: 2,
                    r: 4,
                  }}
                />
              );
            })}
          </Component>
        </ResponsiveContainer>
      ) : (
        <Skeleton className={"min-h-72 h-full mt-4 w-full rounded-lg"} />
      )}
    </div>
  );
};

MultiChartCard.propTypes = {
  charts: PropTypes.arrayOf(
    PropTypes.shape({
      dataKey: PropTypes.string,
      color: PropTypes.string,
      label: PropTypes.string,
    }),
  ),
  title: PropTypes.string,
  description: PropTypes.string,
  className: PropTypes.string,
  type: PropTypes.oneOf(["bar", "area"]),
  data: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      uv: PropTypes.number,
      pv: PropTypes.number,
    }),
  ),
  xAxisTickFormatter: PropTypes.func,
  CustomXTick: PropTypes.func,
  isLoading: PropTypes.bool,
};

export default MultiChartCard;

const renderTooltipContent = (payload, charts) => {
  if (!payload || payload.length === 0) return null;
  const data = payload[0].payload;
  return (
    <div className="rtl flex min-w-20 flex-col items-center justify-start rounded-md bg-background shadow-small">
      {charts.map((chart) => {
        return (
          <Fragment key={chart.title}>
            <div className="w-full rounded-t-md bg-background-100 p-1 text-center">
              <p>{chart.title}</p>
            </div>
            <div className="flex items-center justify-center gap-1 p-2 text-center">
              <p>
                {new Intl.NumberFormat("fa-IR").format(data[chart.dataKey])}
              </p>
              {/* <TomanIcon className='size-3' /> */}
              <span
                className="ms-1.5 size-2.5 rounded-full"
                style={{
                  backgroundColor: `hsl(var(--heroui-${chart.color}))`,
                }}
              />
            </div>
          </Fragment>
        );
      })}
    </div>
  );
};

const topContent = (charts, data) => (
  <div className="flex flex-wrap items-start gap-6">
    {charts.map((chart) => {
      const totalVal = Number(
        data.reduce((acc, item) => acc + item[chart.dataKey], 0) / data.length,
      ).toFixed(0);
      return (
        <div key={chart.dataKey} className="flex items-start gap-3">
          <span
            className="mt-1 size-3 rounded-full"
            style={{
              backgroundColor: `hsl(var(--heroui-${chart.color}))`,
            }}
          />
          <div className="flex flex-col gap-1">
            <p>{chart.label}</p>
            <span className="flex items-center gap-2 text-sm font-medium sm:text-base md:text-large">
              {new Intl.NumberFormat("fa-IR").format(totalVal)}
              {/* <TomanIcon className='mb-1 size-3 sm:size-4' /> */}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);
